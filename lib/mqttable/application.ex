defmodule Mqttable.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      MqttableWeb.Telemetry,
      {DNSCluster, query: Application.get_env(:mqttable, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Mqttable.PubSub},
      # Start the ConnectionSets server
      Mqttable.ConnectionSets.Server,
      {Tz.UpdatePeriodically, [interval_in_days: 7]},
      # Start the MQTT client manager
      Mqttable.MqttClient.Manager,
      # Start the trace manager
      Mqttable.TraceManager,
      # Start to serve requests, typically the last entry
      MqttableWeb.Endpoint
    ]

    # Start the EMQTT Logger Handler
    Mqttable.EmqttLogger.setup()

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    # Configure more lenient restart strategy to handle MQTT connection failures
    opts = [
      strategy: :one_for_one,
      name: Mqttable.Supervisor,
      # Allow more restarts over a longer period to handle network issues
      max_restarts: 10,
      max_seconds: 60
    ]

    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    MqttableWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
