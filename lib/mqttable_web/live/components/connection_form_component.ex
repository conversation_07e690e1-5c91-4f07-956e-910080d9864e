defmodule MqttableWeb.ConnectionFormComponent do
  @moduledoc """
  Shared functionality for connection form components.
  This module contains common functions and components used by both
  NewConnectionModalComponent and EditConnectionModalComponent.
  """
  use MqttableWeb, :html

  alias Mqttable.Encryption

  # Shared UI component for connection header
  def connection_header(assigns) do
    ~H"""
    <div class="bg-base-200 p-2 rounded-lg mb-4 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <%= case @connection_set.protocol do %>
          <% "mqtt" -> %>
            <img src="/images/mqtt_protocol.svg" class="h-6 w-6" alt="MQTT Protocol" />
          <% "mqtts" -> %>
            <img src="/images/mqtts_protocol.svg" class="h-6 w-6" alt="MQTTS Protocol" />
          <% "ws" -> %>
            <img src="/images/ws_protocol.svg" class="h-6 w-6" alt="WS Protocol" />
          <% "wss" -> %>
            <img src="/images/wss_protocol.svg" class="h-6 w-6" alt="WSS Protocol" />
          <% "quic" -> %>
            <img src="/images/quic_protocol.svg" class="h-6 w-6" alt="QUIC Protocol" />
          <% _ -> %>
            <.icon name="hero-square-3-stack-3d" class="h-6 w-6" />
        <% end %>
        <span class="font-medium">
          {@connection_set.protocol}://{@connection_set.host}:{@connection_set.port}
        </span>
      </div>
      <span class="font-medium text-gray-700">
        {@connection_set.name}
      </span>
    </div>
    """
  end

  # Shared UI component for general section
  def general_section(assigns) do
    ~H"""
    <div class="bg-base-200 p-4 rounded-lg">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="form-control md:col-span-2">
          <div class="flex w-full">
            <label class="input input-bordered flex items-center gap-2 w-full">
              <span class="text-gray-500">Name</span>
              <input
                type="text"
                name="connection[name]"
                value={@edit_connection.name}
                class="grow"
                placeholder="Connection name"
                required
              />
              <button type="button" class="btn btn-sm btn-ghost" phx-click="generate_connection_name">
                <.icon name="hero-arrow-path" class="h-4 w-4" />
              </button>
            </label>
          </div>
        </div>
        
    <!-- Status section has been removed -->
        <input
          type="hidden"
          name="connection[status]"
          value={Map.get(@edit_connection, :status, "disconnected")}
        />

        <div class="form-control md:col-span-2">
          <label class="label">
            <span class="label-text">Client ID</span>
          </label>
          <div class="flex w-full">
            <label class="input input-bordered flex items-center gap-2 w-full">
              <img src="/images/device.svg" class="h-5 w-5" alt="Device" />
              <input
                type="text"
                name="connection[client_id]"
                value={@edit_connection.client_id}
                class="grow"
                placeholder="Client ID"
                required
              />
              <button type="button" class="btn btn-sm btn-ghost" phx-click="generate_client_id">
                <.icon name="hero-arrow-path" class="h-4 w-4" />
              </button>
            </label>
          </div>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">Username</span>
          </label>
          <input
            type="text"
            name="connection[username]"
            value={@edit_connection.username}
            class="input input-bordered w-full"
            placeholder="Username"
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text">Password</span>
          </label>
          <input
            type="password"
            name="connection[password]"
            value={Encryption.decrypt(@edit_connection.password)}
            class="input input-bordered w-full"
            placeholder="Password"
          />
        </div>
      </div>
    </div>
    """
  end

  # Helper function to render the advanced section
  def advanced_section(assigns) do
    ~H"""
    <div class="bg-base-200 p-4 rounded-lg">
      <div class="mb-4">
        <!-- MQTT Version独占一行 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text">MQTT Version</span>
          </label>
          <select
            name="connection[mqtt_version]"
            class="select select-bordered w-full"
            phx-change="mqtt_version_changed"
          >
            <option value="5.0" selected={@edit_connection.mqtt_version == "5.0"}>
              5.0
            </option>
            <option value="3.1.1" selected={@edit_connection.mqtt_version == "3.1.1"}>
              3.1.1
            </option>
            <option value="3.1" selected={@edit_connection.mqtt_version == "3.1"}>
              3.1
            </option>
          </select>
        </div>
      </div>

      <%= if @edit_connection.mqtt_version in ["3.1", "3.1.1"] do %>
        <!-- MQTT 3.1/3.1.1 Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Keep Alive (seconds)</span>
            </label>
            <input
              type="number"
              name="connection[keep_alive]"
              value={@edit_connection.keep_alive}
              class="input input-bordered w-full"
              placeholder="60"
              min="0"
            />
          </div>

          <div class="form-control flex items-center h-full pt-8">
            <input
              type="checkbox"
              name="connection[clean_start]"
              checked={@edit_connection.clean_start}
              class="checkbox checkbox-primary mr-2"
            />
            <span class="label-text">Clean Session</span>
          </div>
        </div>
      <% else %>
        <!-- MQTT 5.0 Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Keep Alive (seconds)</span>
            </label>
            <input
              type="number"
              name="connection[keep_alive]"
              value={@edit_connection.keep_alive}
              class="input input-bordered w-full"
              placeholder="60"
              min="0"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Session Expiry Interval (seconds)</span>
            </label>
            <input
              type="number"
              name="connection[session_expiry_interval]"
              value={@edit_connection.session_expiry_interval}
              class="input input-bordered w-full"
              placeholder="0"
              min="0"
            />
          </div>
        </div>
        
    <!-- Three Maximum fields in one row -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Receive Maximum</span>
            </label>
            <input
              type="number"
              name="connection[receive_maximum]"
              value={
                if @edit_connection.receive_maximum == nil or
                     @edit_connection.receive_maximum == "null",
                   do: "",
                   else: @edit_connection.receive_maximum
              }
              class="input input-bordered w-full"
              placeholder="65535"
              min="0"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Topic Alias Maximum</span>
            </label>
            <input
              type="number"
              name="connection[topic_alias_maximum]"
              value={
                if @edit_connection.topic_alias_maximum == nil or
                     @edit_connection.topic_alias_maximum == "null",
                   do: "",
                   else: @edit_connection.topic_alias_maximum
              }
              class="input input-bordered w-full"
              placeholder="0"
              min="0"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Maximum Packet Size</span>
            </label>
            <input
              type="number"
              name="connection[maximum_packet_size]"
              value={
                if @edit_connection.maximum_packet_size == nil or
                     @edit_connection.maximum_packet_size == "null",
                   do: "",
                   else: @edit_connection.maximum_packet_size
              }
              class="input input-bordered w-full"
              placeholder="0"
              min="0"
            />
          </div>
        </div>
        
    <!-- Three checkboxes in one row -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-4">
          <div class="form-control flex items-center">
            <input
              type="checkbox"
              name="connection[clean_start]"
              checked={@edit_connection.clean_start}
              class="checkbox checkbox-primary mr-2"
            />
            <span class="label-text">Clean Start</span>
          </div>

          <div class="form-control flex items-center">
            <input
              type="checkbox"
              name="connection[request_problem_info]"
              checked={@edit_connection.request_problem_info}
              class="checkbox checkbox-primary mr-2"
            />
            <span class="label-text">Request Problem Info</span>
          </div>

          <div class="form-control flex items-center">
            <input
              type="checkbox"
              name="connection[request_response_info]"
              checked={@edit_connection.request_response_info}
              class="checkbox checkbox-primary mr-2"
            />
            <span class="label-text">Request Response Info</span>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper function to render the user properties section
  def user_properties_section(assigns) do
    # Check if there are any user properties with non-empty keys
    has_properties_with_keys =
      assigns.edit_connection.user_properties != [] &&
        Enum.any?(assigns.edit_connection.user_properties, fn prop ->
          prop.key != nil && prop.key != ""
        end)

    assigns = assign(assigns, :has_properties_with_keys, has_properties_with_keys)

    ~H"""
    <div class="collapse collapse-arrow bg-base-200">
      <input type="checkbox" checked={@has_properties_with_keys} id="user-properties-collapse" />
      <div class="collapse-title font-medium">
        User Properties
      </div>
      <div class="collapse-content">
        <table class="table table-zebra w-full table-with-dividers">
          <thead>
            <tr>
              <th>Key</th>
              <th>Value</th>
              <th class="w-16"></th>
            </tr>
          </thead>
          <tbody id="user-properties">
            <%= for {prop, index} <- Enum.with_index(@edit_connection.user_properties) do %>
              <tr>
                <td class="px-2">
                  <input
                    type="text"
                    name={"connection[user_properties][#{index}][key]"}
                    value={prop.key}
                    placeholder="Key"
                    class="input input-sm w-full border-0"
                  />
                </td>
                <td class="px-2">
                  <input
                    type="text"
                    name={"connection[user_properties][#{index}][value]"}
                    value={prop.value}
                    placeholder="Value"
                    class="input input-sm w-full border-0"
                  />
                </td>
                <td class="px-2">
                  <button
                    type="button"
                    phx-click="delete_user_property"
                    phx-value-index={index}
                    class="btn btn-ghost btn-xs text-error"
                  >
                    <.icon name="hero-minus-circle" class="h-4 w-4" />
                  </button>
                </td>
              </tr>
            <% end %>
            <tr>
              <td colspan="3" class="text-center">
                <button
                  type="button"
                  phx-click="add_user_property"
                  class="btn btn-ghost btn-sm text-primary"
                >
                  <.icon name="hero-plus-circle" class="h-4 w-4 mr-1" /> Add Property
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    """
  end

  # Helper function to render the Last Will and Testament section
  def lwt_section(assigns) do
    ~H"""
    <div class="collapse collapse-arrow bg-base-200">
      <input type="checkbox" id="lwt-collapse" />
      <div class="collapse-title font-medium">
        Last Will and Testament
      </div>
      <div class="collapse-content">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">Last-Will Topic</span>
            </label>
            <input
              type="text"
              name="connection[will_topic]"
              value={@edit_connection.will_topic}
              class="input input-bordered w-full"
              placeholder="Topic"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text">Last-Will QoS</span>
            </label>
            <select name="connection[will_qos]" class="select select-bordered w-full">
              <option
                value="0"
                selected={@edit_connection.will_qos == "0" || @edit_connection.will_qos == 0}
              >
                0
              </option>
              <option
                value="1"
                selected={@edit_connection.will_qos == "1" || @edit_connection.will_qos == 1}
              >
                1
              </option>
              <option
                value="2"
                selected={@edit_connection.will_qos == "2" || @edit_connection.will_qos == 2}
              >
                2
              </option>
            </select>
          </div>

          <div class="form-control md:col-span-2">
            <label class="label">
              <span class="label-text">Last-Will Payload</span>
            </label>
            <textarea
              name="connection[will_payload]"
              class="textarea textarea-bordered w-full"
              rows="3"
              placeholder="Payload"
            ><%= @edit_connection.will_payload %></textarea>
          </div>

          <%= if @edit_connection.mqtt_version == "5.0" do %>
            <div class="form-control">
              <label class="label">
                <span class="label-text">Will Delay Interval (seconds)</span>
              </label>
              <input
                type="number"
                name="connection[will_delay_interval]"
                value={@edit_connection.will_delay_interval}
                class="input input-bordered w-full"
                placeholder="0"
                min="0"
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Message Expiry Interval (seconds)</span>
              </label>
              <input
                type="number"
                name="connection[will_message_expiry]"
                value={@edit_connection.will_message_expiry}
                class="input input-bordered w-full"
                placeholder="0"
                min="0"
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Content Type</span>
              </label>
              <input
                type="text"
                name="connection[will_content_type]"
                value={@edit_connection.will_content_type}
                class="input input-bordered w-full"
                placeholder="application/json"
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Response Topic</span>
              </label>
              <input
                type="text"
                name="connection[will_response_topic]"
                value={@edit_connection.will_response_topic}
                class="input input-bordered w-full"
                placeholder="Response topic"
              />
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text">Correlation Data</span>
              </label>
              <input
                type="text"
                name="connection[will_correlation_data]"
                value={@edit_connection.will_correlation_data}
                class="input input-bordered w-full"
                placeholder="Correlation data"
              />
            </div>
          <% end %>

          <div class="form-control">
            <div class="flex items-center h-full pt-8">
              <input
                type="checkbox"
                name="connection[will_retain]"
                checked={@edit_connection.will_retain}
                class="checkbox checkbox-primary mr-2"
              />
              <span class="label-text">Retain Last-Will Message</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
