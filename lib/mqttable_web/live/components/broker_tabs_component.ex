defmodule MqttableWeb.BrokerTabsComponent do
  @moduledoc """
  LiveComponent for rendering broker tabs in a VSCode-like interface.
  This component displays brokers as tabs with drag-and-drop reordering capability.
  """
  use MqttableWeb, :live_component

  import MqttableWeb.CoreComponents

  @impl true
  def render(assigns) do
    ~H"""
    <div class="broker-tabs-container bg-base-100 py-1 px-2">
      <div class="flex items-center">
        <!-- Broker Tabs -->
        <div
          id="broker-tabs-sortable"
          class="flex items-center flex-1 overflow-x-auto"
          phx-hook="BrokerTabsSortable"
        >
          <%= if Enum.empty?(@connection_sets) do %>
            <div class="flex items-center text-base-content/60 text-sm px-3 py-2">
              <.icon name="hero-information-circle" class="w-4 h-4 mr-2" />
              No brokers available. Click the + button to create one.
            </div>
          <% else %>
            <%= for broker <- @connection_sets do %>
              <div
                class={[
                  "broker-tab group flex items-center px-2 py-1 mr-1 rounded-lg cursor-pointer transition-all duration-200 min-w-0 max-w-60",
                  "hover:bg-base-300",
                  if(@active_connection_set && @active_connection_set.name == broker.name) do
                    "bg-base-100 border-2 border-yellow-500 text-base-content font-medium shadow-sm"
                  else
                    "bg-base-200 border-2 border-transparent text-base-content/80 hover:text-base-content"
                  end
                ]}
                phx-click="select_broker_tab"
                phx-value-name={broker.name}
                data-broker-name={broker.name}
              >
                <!-- Broker Name -->
                <span class="truncate text-sm">
                  {broker.name}
                </span>
                
    <!-- Close Button (only show on hover for inactive tabs, always show for active) -->
                <span
                  class={[
                    "ml-2 p-1 rounded hover:bg-error/20 text-error/60 hover:text-error transition-colors flex-shrink-0 cursor-pointer",
                    if(@active_connection_set && @active_connection_set.name == broker.name) do
                      "opacity-100"
                    else
                      "opacity-0 group-hover:opacity-100"
                    end
                  ]}
                  phx-click="close_broker_tab"
                  phx-value-name={broker.name}
                >
                  <.icon name="hero-x-mark" class="w-3 h-3" />
                </span>
              </div>
            <% end %>
          <% end %>
        </div>
        
    <!-- Add New Broker Button -->
        <button
          type="button"
          class="btn btn-primary btn-sm ml-2 flex-shrink-0"
          phx-click="open_connection_set_modal"
          phx-value-type="new_connection_set"
          title="Add new broker"
        >
          +
        </button>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end
end
