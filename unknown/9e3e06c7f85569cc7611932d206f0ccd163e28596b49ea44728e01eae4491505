defmodule Mqttable.Uploads.CertificateHandler do
  @moduledoc """
  Module for handling certificate file uploads.
  Provides centralized functionality for processing and managing certificate files.
  """
  require Logger

  # Certificate directory constants
  @certificates_dir "priv/data/certificates"

  # Upload options for certificate files
  @upload_options [
    accept: ~w(application/x-pem-file application/x-x509-ca-cert application/x-pkcs8),
    max_entries: 1,
    # 5MB should be sufficient for most certificates
    max_file_size: 5_000_000
  ]

  @doc """
  Returns the standard upload options for certificate files.
  """
  def upload_options, do: @upload_options

  @doc """
  Returns the path to the certificates directory.
  """
  def certificates_dir, do: @certificates_dir

  @doc """
  Ensures the certificates directory exists.
  """
  def ensure_certificates_dir do
    certificates_dir = Path.join([:code.priv_dir(:mqttable), "data", "certificates"])
    File.mkdir_p!(certificates_dir)
    certificates_dir
  end

  @doc """
  Processes an uploaded certificate file.

  ## Parameters
  - `socket`: The LiveView socket
  - `upload_name`: The name of the upload field (e.g., :ca_file)
  - `existing_path`: The existing file path (if any)

  ## Returns
  - `{path, socket}`: The path to the uploaded file and the updated socket
  """
  def process_uploaded_file(socket, upload_name, existing_path) do
    # Ensure the certificates directory exists
    certificates_dir = ensure_certificates_dir()

    # Check if there are any uploaded entries for this upload name
    uploaded_files =
      Phoenix.LiveView.Upload.consume_uploaded_entries(socket, upload_name, fn %{path: path},
                                                                               entry ->
        # Generate a unique filename with the original file extension
        original_filename = entry.client_name
        # Extract the file extension
        ext = Path.extname(original_filename)
        # Generate a unique filename with timestamp
        now = DateTime.utc_now()

        formatted_timestamp =
          "#{now.year}#{pad_number(now.month)}#{pad_number(now.day)}#{pad_number(now.hour)}#{pad_number(now.minute)}#{pad_number(now.second)}_#{pad_number(now.microsecond |> elem(0), 6)}"

        # Remove "file" from the upload_name (e.g., ca_file -> ca)
        clean_name = Atom.to_string(upload_name) |> String.replace("_file", "")
        unique_name = "#{clean_name}_#{formatted_timestamp}#{ext}"

        # Create destination path in certificates directory
        dest = Path.join(certificates_dir, unique_name)

        # Copy the file to the destination
        File.cp!(path, dest)

        # Return the path relative to the certificates directory
        {:ok, "/certificates/#{unique_name}"}
      end)

    # If there are uploaded files, use the first one, otherwise keep the existing path
    case uploaded_files do
      [path | _] -> {path, socket}
      [] -> {existing_path, socket}
    end
  end

  @doc """
  Cleans up unused certificate files in the certificates directory.

  ## Parameters
  - `connection_sets`: List of connection sets to check for certificate references
  - `max_age_hours`: Maximum age in hours for unused certificates before deletion (default: 1)
  """
  def cleanup_unused_certificates(connection_sets, max_age_hours \\ 1) do
    # Get the certificates directory path
    certificates_dir = Path.join([:code.priv_dir(:mqttable), "data", "certificates"])

    # Ensure the directory exists
    unless File.exists?(certificates_dir) do
      # Skip cleanup if directory doesn't exist
      :ok
    else
      # Extract all certificate file paths from connection sets
      cert_filenames =
        connection_sets
        |> Enum.flat_map(fn set ->
          # Get broker-level certificate paths
          [
            Map.get(set, :ca_file),
            Map.get(set, :client_cert_file),
            Map.get(set, :client_key_file)
          ]
          |> Enum.filter(&(is_binary(&1) && &1 != ""))
          |> Enum.map(&Path.basename/1)
        end)
        |> Enum.uniq()

      # Get all files in the certificates directory
      case File.ls(certificates_dir) do
        {:ok, all_files} ->
          # Check which files are not referenced in any connection set
          unused_files = all_files -- cert_filenames

          # Only proceed if there are unused files to check
          unless Enum.empty?(unused_files) do
            # Calculate the age threshold in seconds
            age_threshold_seconds = max_age_hours * 3600
            current_time = :os.system_time(:second)

            # Check each unused file
            Enum.each(unused_files, fn file ->
              file_path = Path.join(certificates_dir, file)

              case File.stat(file_path, time: :posix) do
                {:ok, stat} ->
                  creation_time = stat.ctime
                  age_in_seconds = current_time - creation_time

                  # Delete files older than the threshold
                  if age_in_seconds > age_threshold_seconds do
                    case File.rm(file_path) do
                      :ok ->
                        Logger.info(
                          "Deleted unused certificate file: #{file} (age: #{age_in_seconds / 3600} hours)"
                        )

                      {:error, _reason} ->
                        # Just skip files we can't delete
                        :ok
                    end
                  end

                {:error, _reason} ->
                  # Skip files we can't stat
                  :ok
              end
            end)
          end

        {:error, reason} ->
          Logger.error("Error listing files in certificates directory: #{reason}")
      end
    end
  end

  defp pad_number(number, length \\ 2) do
    number
    |> Integer.to_string()
    |> String.pad_leading(length, "0")
  end
end
