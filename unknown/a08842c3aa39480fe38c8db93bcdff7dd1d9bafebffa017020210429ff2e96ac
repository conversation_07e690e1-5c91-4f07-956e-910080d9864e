defmodule MqttableWeb.Variables.Manager do
  @moduledoc """
  Module for managing variables.
  This module provides functions for creating, updating, and deleting variables.
  """

  import Phoenix.Component

  # Import verified routes
  use Phoenix.VerifiedRoutes,
    endpoint: MqttableWeb.Endpoint,
    router: MqttableWeb.Router,
    statics: MqttableWeb.static_paths()

  @doc """
  Handles the save_variable event.
  """
  def handle_save_variable(socket, %{"variable" => var_params}) do
    name = var_params["name"]
    value = var_params["value"] || ""
    enabled = var_params["enabled"] == "on"

    new_var = %{
      name: name,
      value: value,
      enabled: enabled
    }

    active_connection_set = socket.assigns.active_connection_set
    updated_vars = Map.get(active_connection_set, :variables, []) ++ [new_var]
    updated_set = Map.put(active_connection_set, :variables, updated_vars)

    connection_sets =
      Enum.map(socket.assigns.connection_sets, fn set ->
        if set.name == active_connection_set.name, do: updated_set, else: set
      end)

    # Save the updated connection sets to persistent storage
    MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

    {:noreply,
     socket
     |> assign(:connection_sets, connection_sets)
     |> assign(:active_connection_set, updated_set)
     |> assign(:show_modal, false)}
  end

  @doc """
  Handles the update_variable event.
  """
  def handle_update_variable(socket, %{"variable" => var_params, "old_name" => old_name}) do
    name = var_params["name"]
    value = var_params["value"] || ""
    enabled = var_params["enabled"] == "on"

    updated_var = %{
      name: name,
      value: value,
      enabled: enabled
    }

    active_connection_set = socket.assigns.active_connection_set

    updated_vars =
      Enum.map(Map.get(active_connection_set, :variables, []), fn var ->
        if var.name == old_name, do: updated_var, else: var
      end)

    updated_set = Map.put(active_connection_set, :variables, updated_vars)

    connection_sets =
      Enum.map(socket.assigns.connection_sets, fn set ->
        if set.name == active_connection_set.name, do: updated_set, else: set
      end)

    # Save the updated connection sets to persistent storage
    MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

    {:noreply,
     socket
     |> assign(:connection_sets, connection_sets)
     |> assign(:active_connection_set, updated_set)
     |> assign(:show_modal, false)
     |> assign(:edit_var, nil)}
  end

  @doc """
  Handles the delete_variable event.
  """
  def handle_delete_variable(socket, %{"name" => name}) do
    # When in the modal, we need to update edit_connection_set, not active_connection_set
    if socket.assigns.show_modal do
      edit_connection_set = socket.assigns.edit_connection_set

      # Remove the variable with the given name
      updated_vars =
        Enum.reject(Map.get(edit_connection_set, :variables, []), fn var -> var.name == name end)

      updated_set = Map.put(edit_connection_set, :variables, updated_vars)

      {:noreply, assign(socket, :edit_connection_set, updated_set)}
    else
      # Original behavior for when not in modal
      active_connection_set = socket.assigns.active_connection_set

      updated_vars =
        Enum.reject(Map.get(active_connection_set, :variables, []), fn var -> var.name == name end)

      updated_set = Map.put(active_connection_set, :variables, updated_vars)

      connection_sets =
        Enum.map(socket.assigns.connection_sets, fn set ->
          if set.name == active_connection_set.name, do: updated_set, else: set
        end)

      # Save the updated connection sets to persistent storage
      MqttableWeb.ConnectionSets.Manager.save_connection_set(connection_sets)

      {:noreply,
       socket
       |> assign(:connection_sets, connection_sets)
       |> assign(:active_connection_set, updated_set)}
    end
  end

  @doc """
  Handles the add_variable_row event.
  """
  def handle_add_variable_row(socket, _params) do
    edit_connection_set = socket.assigns.edit_connection_set
    variables = Map.get(edit_connection_set, :variables, [])

    # Add a new empty row
    new_var = %{name: "", value: "", enabled: true}
    updated_vars = variables ++ [new_var]

    # Update the connection set with the new variables list
    updated_set = Map.put(edit_connection_set, :variables, updated_vars)

    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end

  @doc """
  Handles the clear_variables event.
  """
  def handle_clear_variables(socket) do
    edit_connection_set = socket.assigns.edit_connection_set

    # Create a new empty variable for the empty row
    empty_var = %{name: "", value: "", enabled: true}

    # Create a new connection set with the same properties but only one empty variable
    updated_set = Map.put(edit_connection_set, :variables, [empty_var])

    # Send a flash message to confirm variables were cleared
    socket = Phoenix.LiveView.put_flash(socket, :info, "Variables cleared")

    # Return the updated socket with the new connection set
    {:noreply, assign(socket, :edit_connection_set, updated_set)}
  end
end
