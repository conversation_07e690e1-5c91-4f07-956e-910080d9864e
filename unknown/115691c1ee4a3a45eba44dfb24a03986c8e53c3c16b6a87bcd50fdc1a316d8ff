<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美化后的订阅列表</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.2/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义字体或额外样式 (可选) */
        body {
            font-family: 'Inter', sans-serif; /* Inter 字体示例 */
        }
        /* 确保 SVG 图标大小一致 */
        .btn-square svg {
            width: 1rem; /* 16px */
            height: 1rem; /* 16px */
        }
        .badge svg {
            width: 0.75rem; /* 12px */
            height: 0.75rem; /* 12px */
        }
    </style>
</head>
<body class="p-4 md:p-8 bg-base-200">

<div class="container mx-auto bg-base-100 p-4 sm:p-6 rounded-xl shadow-lg">
    <h1 class="text-2xl font-bold mb-6 text-center sm:text-left">MQTT 客户端订阅管理</h1>

    <div class="overflow-x-auto rounded-lg shadow">
      <table class="table w-full table-zebra">
        <thead class="bg-base-300 text-base-content">
          <tr>
            <th class="p-3 sm:p-4">连接信息</th>
            <th class="p-3 sm:p-4">状态</th>
            <th class="p-3 sm:p-4">连接时间</th>
            <th class="p-3 sm:p-4">订阅列表</th>
            <th class="p-3 sm:p-4 text-center">操作 (客户端)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="p-3 sm:p-4 align-top">
              <div class="font-medium text-base-content">ID: mqttable_e2f43d2b</div>
              <div class="text-sm opacity-80">Publisher_b02c</div>
            </td>
            <td class="p-3 sm:p-4 align-top">
              <span class="badge badge-success badge-outline font-semibold">
                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"/></svg>
                已连接
              </span>
            </td>
            <td class="p-3 sm:p-4 align-top text-sm">2025-05-22T23:59:15Z</td>
            <td class="p-3 sm:p-4 align-top">
              <div class="space-y-3 max-w-md">
                <div class="p-3 border border-base-300 rounded-lg shadow-sm bg-base-100 hover:shadow-md transition-shadow duration-200 ease-in-out">
                  <div class="flex justify-between items-start mb-2">
                    <span class="font-semibold text-base-content break-all">test1/finance/updates/long/topic/name</span>
                    <div class="flex-shrink-0 ml-2 space-x-1">
                      <button class="btn btn-ghost btn-xs btn-square" aria-label="编辑订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                      </button>
                      <button class="btn btn-ghost btn-xs btn-square text-error" aria-label="删除订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                      </button>
                    </div>
                  </div>
                  <div class="flex flex-wrap gap-2 items-center text-xs">
                    <span class="badge badge-info gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                      QoS 1
                    </span>
                    <span class="badge badge-accent gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2.5 2v6h6M2.66 15.57a10 10 0 1 0 .57-8.38"/> </svg>
                      RH 2
                    </span>
                    <span class="badge badge-secondary gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>
                      ID: 12
                    </span>
                    <span class="badge badge-warning gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18.36 6.64-1.42-1.42a4 4 0 0 0-5.65 0l-5.01 5.01L2 22l5.09-1.02L17.1 5.02a4 4 0 0 0 1.26-3.38z"></path><path d="m2 22 1.17-1.17"></path></svg>
                      Retain
                    </span>
                     <span class="badge badge-neutral gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/><path d="M13.73 21a2 2 0 0 1-3.46 0"/></svg>
                      No Local
                    </span>
                  </div>
                </div>

                <div class="p-3 border border-base-300 rounded-lg shadow-sm bg-base-100 hover:shadow-md transition-shadow duration-200 ease-in-out">
                  <div class="flex justify-between items-start mb-2">
                    <span class="font-semibold text-base-content break-all">dfd/alerts</span>
                     <div class="flex-shrink-0 ml-2 space-x-1">
                      <button class="btn btn-ghost btn-xs btn-square" aria-label="编辑订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                      </button>
                      <button class="btn btn-ghost btn-xs btn-square text-error" aria-label="删除订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                      </button>
                    </div>
                  </div>
                  <div class="flex flex-wrap gap-2 items-center text-xs">
                    <span class="badge badge-info gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                      QoS 0
                    </span>
                     <span class="badge badge-outline gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                      No Retain
                    </span>
                  </div>
                </div>
                <button class="btn btn-sm btn-outline btn-primary mt-3 w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                  为此客户端添加新订阅
                </button>
              </div>
            </td>
            <td class="p-3 sm:p-4 align-middle text-center">
              <button class="btn btn-ghost btn-sm btn-square text-error" aria-label="删除此客户端">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>
              </button>
            </td>
          </tr>

          <tr>
            <td class="p-3 sm:p-4 align-top">
              <div class="font-medium text-base-content">ID: mqttable_45747ddb</div>
              <div class="text-sm opacity-80">Device_ea70</div>
            </td>
            <td class="p-3 sm:p-4 align-top">
              <span class="badge badge-error badge-outline font-semibold">
                <svg xmlns="http://www.w3.org/2000/svg" class="mr-1" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                已断开
              </span>
            </td>
            <td class="p-3 sm:p-4 align-top text-sm">-</td>
            <td class="p-3 sm:p-4 align-top">
              <div class="space-y-3 max-w-md">
                <div class="p-3 border border-base-300 rounded-lg shadow-sm bg-base-100 hover:shadow-md transition-shadow duration-200 ease-in-out">
                  <div class="flex justify-between items-start mb-2">
                    <span class="font-semibold text-base-content break-all">test/sensors</span>
                    <div class="flex-shrink-0 ml-2 space-x-1">
                      <button class="btn btn-ghost btn-xs btn-square" aria-label="编辑订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg>
                      </button>
                      <button class="btn btn-ghost btn-xs btn-square text-error" aria-label="删除订阅">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                      </button>
                    </div>
                  </div>
                  <div class="text-xs opacity-70 italic">无详细属性或客户端已离线</div>
                </div>
                 <button class="btn btn-sm btn-outline btn-primary mt-3 w-full">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                  为此客户端添加新订阅
                </button>
              </div>
            </td>
            <td class="p-3 sm:p-4 align-middle text-center">
              <button class="btn btn-ghost btn-sm btn-square text-error" aria-label="删除此客户端">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
</div>

</body>
</html>

