defmodule MqttableWeb.BrokerTabsComponentTest do
  use MqttableWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias MqttableWeb.BrokerTabsComponent

  describe "BrokerTabsComponent" do
    test "renders empty state when no brokers are available" do
      assigns = %{
        connection_sets: [],
        active_connection_set: nil
      }

      html = render_component(BrokerTabsComponent, assigns)

      assert html =~ "No brokers available"
      assert html =~ "Click the + button to create one"
      assert html =~ "hero-information-circle"
    end

    test "renders broker tabs when brokers are available" do
      brokers = [
        %{name: "Local Broker"},
        %{name: "Remote Broker"}
      ]

      assigns = %{
        connection_sets: brokers,
        active_connection_set: %{name: "Local Broker"}
      }

      html = render_component(BrokerTabsComponent, assigns)

      assert html =~ "Local Broker"
      assert html =~ "Remote Broker"
      refute html =~ "No brokers available"
    end

    test "highlights active broker tab" do
      brokers = [
        %{name: "Local Broker"},
        %{name: "Remote Broker"}
      ]

      assigns = %{
        connection_sets: brokers,
        active_connection_set: %{name: "Local Broker"}
      }

      html = render_component(BrokerTabsComponent, assigns)

      # Active tab should have yellow border (full border) and different styling
      assert html =~ "border-2 border-yellow-500"
      assert html =~ "bg-base-100"
    end

    test "renders add broker button" do
      assigns = %{
        connection_sets: [],
        active_connection_set: nil
      }

      html = render_component(BrokerTabsComponent, assigns)

      assert html =~ "Add new broker"
      assert html =~ "+"
      assert html =~ "btn btn-primary btn-sm"
    end

    test "includes sortable hook for drag and drop" do
      assigns = %{
        connection_sets: [%{name: "Test Broker"}],
        active_connection_set: nil
      }

      html = render_component(BrokerTabsComponent, assigns)

      assert html =~ "phx-hook=\"BrokerTabsSortable\""
      assert html =~ "id=\"broker-tabs-sortable\""
    end

    test "renders close buttons for broker tabs" do
      brokers = [
        %{name: "Local Broker"}
      ]

      assigns = %{
        connection_sets: brokers,
        active_connection_set: %{name: "Local Broker"}
      }

      html = render_component(BrokerTabsComponent, assigns)

      assert html =~ "phx-click=\"close_broker_tab\""
      assert html =~ "hero-x-mark"
      assert html =~ "phx-value-name=\"Local Broker\""
    end
  end
end
